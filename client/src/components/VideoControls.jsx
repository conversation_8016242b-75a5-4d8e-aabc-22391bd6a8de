import { useState, useCallback } from 'react';
import { Button } from './ui/button';
import { Switch } from './ui/switch';
import { Slider } from './ui/slider';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { Card, CardContent } from './ui/card';
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  PhoneOff,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Settings
} from 'lucide-react';

const VideoControls = ({
  isVideoEnabled = true,
  isAudioEnabled = true,
  isCallActive = false,
  volume = 50,
  isFullscreen = false,
  onVideoToggle,
  onAudioToggle,
  onEndCall,
  onVolumeChange,
  onFullscreenToggle,
  onSettingsClick,
  className = ''
}) => {
  const [isMuted, setIsMuted] = useState(false);

  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted);
    onVolumeChange?.(isMuted ? volume : 0);
  }, [isMuted, volume, onVolumeChange]);

  const handleVolumeSliderChange = useCallback((value) => {
    const newVolume = value[0];
    setIsMuted(newVolume === 0);
    onVolumeChange?.(newVolume);
  }, [onVolumeChange]);

  return (
    <TooltipProvider>
      <Card className={`bg-black/80 backdrop-blur-sm border-gray-700 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center gap-4">
            {/* Video Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isVideoEnabled ? "default" : "destructive"}
                  size="lg"
                  className="rounded-full w-12 h-12 p-0"
                  onClick={onVideoToggle}
                >
                  {isVideoEnabled ? (
                    <Video className="w-5 h-5" />
                  ) : (
                    <VideoOff className="w-5 h-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isVideoEnabled ? 'Turn off camera' : 'Turn on camera'}</p>
              </TooltipContent>
            </Tooltip>

            {/* Audio Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isAudioEnabled ? "default" : "destructive"}
                  size="lg"
                  className="rounded-full w-12 h-12 p-0"
                  onClick={onAudioToggle}
                >
                  {isAudioEnabled ? (
                    <Mic className="w-5 h-5" />
                  ) : (
                    <MicOff className="w-5 h-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isAudioEnabled ? 'Mute microphone' : 'Unmute microphone'}</p>
              </TooltipContent>
            </Tooltip>

            {/* End Call */}
            {isCallActive && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="destructive"
                    size="lg"
                    className="rounded-full w-12 h-12 p-0 bg-red-600 hover:bg-red-700"
                    onClick={onEndCall}
                  >
                    <PhoneOff className="w-5 h-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>End call</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Volume Control */}
            <div className="flex items-center gap-2 bg-gray-800/50 rounded-lg px-3 py-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto text-white hover:bg-gray-700"
                    onClick={handleVolumeToggle}
                  >
                    {isMuted || volume === 0 ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isMuted ? 'Unmute' : 'Mute'}</p>
                </TooltipContent>
              </Tooltip>
              
              <Slider
                value={[isMuted ? 0 : volume]}
                onValueChange={handleVolumeSliderChange}
                max={100}
                step={1}
                className="w-20"
              />
            </div>

            {/* Fullscreen Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="lg"
                  className="rounded-full w-12 h-12 p-0 border-gray-600 text-white hover:bg-gray-700"
                  onClick={onFullscreenToggle}
                >
                  {isFullscreen ? (
                    <Minimize className="w-5 h-5" />
                  ) : (
                    <Maximize className="w-5 h-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}</p>
              </TooltipContent>
            </Tooltip>

            {/* Settings */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="lg"
                  className="rounded-full w-12 h-12 p-0 border-gray-600 text-white hover:bg-gray-700"
                  onClick={onSettingsClick}
                >
                  <Settings className="w-5 h-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Settings</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

export default VideoControls;
