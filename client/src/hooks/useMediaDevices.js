import { useState, useEffect, useCallback } from 'react';

export const useMediaDevices = () => {
  const [devices, setDevices] = useState({
    audioInputs: [],
    videoInputs: [],
    audioOutputs: []
  });
  const [selectedDevices, setSelectedDevices] = useState({
    audioInput: '',
    videoInput: '',
    audioOutput: ''
  });
  const [permissions, setPermissions] = useState({
    camera: false,
    microphone: false
  });

  // Get available media devices
  const getDevices = useCallback(async () => {
    try {
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      
      const audioInputs = deviceList.filter(device => device.kind === 'audioinput');
      const videoInputs = deviceList.filter(device => device.kind === 'videoinput');
      const audioOutputs = deviceList.filter(device => device.kind === 'audiooutput');

      setDevices({
        audioInputs,
        videoInputs,
        audioOutputs
      });

      // Set default devices if none selected
      if (!selectedDevices.audioInput && audioInputs.length > 0) {
        setSelectedDevices(prev => ({
          ...prev,
          audioInput: audioInputs[0].deviceId
        }));
      }
      if (!selectedDevices.videoInput && videoInputs.length > 0) {
        setSelectedDevices(prev => ({
          ...prev,
          videoInput: videoInputs[0].deviceId
        }));
      }
      if (!selectedDevices.audioOutput && audioOutputs.length > 0) {
        setSelectedDevices(prev => ({
          ...prev,
          audioOutput: audioOutputs[0].deviceId
        }));
      }
    } catch (error) {
      console.error('Error getting media devices:', error);
    }
  }, [selectedDevices.audioInput, selectedDevices.videoInput, selectedDevices.audioOutput]);

  // Check permissions
  const checkPermissions = useCallback(async () => {
    try {
      const cameraPermission = await navigator.permissions.query({ name: 'camera' });
      const microphonePermission = await navigator.permissions.query({ name: 'microphone' });
      
      setPermissions({
        camera: cameraPermission.state === 'granted',
        microphone: microphonePermission.state === 'granted'
      });
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  }, []);

  // Request permissions and get user media
  const requestUserMedia = useCallback(async (constraints = { video: true, audio: true }) => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: constraints.video ? {
          deviceId: selectedDevices.videoInput ? { exact: selectedDevices.videoInput } : undefined
        } : false,
        audio: constraints.audio ? {
          deviceId: selectedDevices.audioInput ? { exact: selectedDevices.audioInput } : undefined
        } : false
      });

      await checkPermissions();
      await getDevices();
      
      return stream;
    } catch (error) {
      console.error('Error requesting user media:', error);
      throw error;
    }
  }, [selectedDevices.audioInput, selectedDevices.videoInput, checkPermissions, getDevices]);

  // Update selected device
  const updateSelectedDevice = useCallback((type, deviceId) => {
    setSelectedDevices(prev => ({
      ...prev,
      [type]: deviceId
    }));
  }, []);

  useEffect(() => {
    checkPermissions();
    getDevices();

    // Listen for device changes
    const handleDeviceChange = () => {
      getDevices();
    };

    navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    };
  }, [checkPermissions, getDevices]);

  return {
    devices,
    selectedDevices,
    permissions,
    requestUserMedia,
    updateSelectedDevice,
    refreshDevices: getDevices
  };
};
