class PeerService {
    constructor() {
        if (!this.peer) {
            this.peer = new RTCPeerConnection({
                iceServers: [
                    {
                        urls: [
                            "stun:stun.l.google.com:19302",
                            "stun:global.stun.twilio.com:3478",
                        ]
                    }
                ]
            })
        }
        this.localStream = null;
        this.remoteStream = null;
    }

    // Add track to peer connection
    addTrack(track, stream) {
        if (this.peer) {
            this.peer.addTrack(track, stream);
        }
    }

    // Remove track from peer connection
    removeTrack(sender) {
        if (this.peer && sender) {
            this.peer.removeTrack(sender);
        }
    }

    // Get all senders
    getSenders() {
        return this.peer ? this.peer.getSenders() : [];
    }

    // Replace track (for switching cameras/microphones)
    async replaceTrack(sender, newTrack) {
        if (sender && newTrack) {
            await sender.replaceTrack(newTrack);
        }
    }

    // Set local stream
    setLocalStream(stream) {
        this.localStream = stream;
    }

    // Get local stream
    getLocalStream() {
        return this.localStream;
    }

    // Set remote stream
    setRemoteStream(stream) {
        this.remoteStream = stream;
    }

    // Get remote stream
    getRemoteStream() {
        return this.remoteStream;
    }

    // Toggle video track
    toggleVideo(enabled) {
        if (this.localStream) {
            const videoTracks = this.localStream.getVideoTracks();
            videoTracks.forEach(track => {
                track.enabled = enabled;
            });
        }
    }

    // Toggle audio track
    toggleAudio(enabled) {
        if (this.localStream) {
            const audioTracks = this.localStream.getAudioTracks();
            audioTracks.forEach(track => {
                track.enabled = enabled;
            });
        }
    }

    // Stop all tracks
    stopAllTracks() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                track.stop();
            });
        }
    }

    // Close peer connection
    close() {
        if (this.peer) {
            this.peer.close();
            this.peer = null;
        }
        this.stopAllTracks();
        this.localStream = null;
        this.remoteStream = null;
    }

    async getAnswer(offer) {
        if (this.peer) {
            await this.peer.setRemoteDescription(offer);
            const ans = await this.peer.createAnswer();
            await this.peer.setLocalDescription(new RTCSessionDescription(ans));
            return ans;
        }
    }

    async setLocalDescription(ans) {
        if (this.peer) {
            await this.peer.setRemoteDescription(new RTCSessionDescription(ans));
        }
    }

    async getOffer() {
        if (this.peer) {
            const offer = await this.peer.createOffer();
            await this.peer.setLocalDescription(new RTCSessionDescription(offer));
            return offer;
        }
    }
}

export default new PeerService();