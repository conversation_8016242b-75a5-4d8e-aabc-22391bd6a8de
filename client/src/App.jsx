import { useCallback, useState, useEffect } from "react";
import { useSocket } from "./context/SocketProvider";
import { useNavigate } from "react-router";
import { Button } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./components/ui/card";
import { Video, Users } from "lucide-react";

function App() {
  const [email, setEmail] = useState("");
  const [room, setRoom] = useState("");
  const socket = useSocket();
  const navigate = useNavigate();

  // console.log(socket);

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      socket.emit("room:join", { email, room });
    },
    [email, room, socket]
  );

  const handleJoinRoom = useCallback(
    (data) => {
      const { email, room } = data;
      navigate(`/room/${room}`);
    },
    [navigate]
  );

  useEffect(() => {
    socket.on("room:join", handleJoinRoom);
    return () => {
      socket.off("room:join", handleJoinRoom);
    };
  }, [socket, handleJoinRoom]);

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4'>
      <div className='w-full max-w-md'>
        <Card className='shadow-xl border-0 bg-white/95 backdrop-blur-sm'>
          <CardHeader className='text-center space-y-2'>
            <div className='mx-auto w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center'>
              <Video className='w-6 h-6 text-white' />
            </div>
            <CardTitle className='text-2xl font-bold text-gray-900'>
              Video Call Lobby
            </CardTitle>
            <CardDescription className='text-gray-600'>
              Enter your details to join a video call room
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className='space-y-6'>
              <div className='space-y-2'>
                <Label
                  htmlFor='email'
                  className='text-sm font-medium text-gray-700'
                >
                  Email Address
                </Label>
                <Input
                  id='email'
                  type='email'
                  placeholder='Enter your email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className='h-11'
                />
              </div>
              <div className='space-y-2'>
                <Label
                  htmlFor='room'
                  className='text-sm font-medium text-gray-700'
                >
                  Room Code
                </Label>
                <Input
                  id='room'
                  type='text'
                  placeholder='Enter room code'
                  value={room}
                  onChange={(e) => setRoom(e.target.value)}
                  required
                  className='h-11'
                />
              </div>
              <Button
                type='submit'
                className='w-full h-11 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium'
                disabled={!email || !room}
              >
                <Users className='w-4 h-4 mr-2' />
                Join Room
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default App;
