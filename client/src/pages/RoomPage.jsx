import { useEffect, useCallback, useState } from "react";
import { useSocket } from "../context/SocketProvider";
import ReactPlayer from "react-player";
import peer from "../service/peer";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import {
  PhoneCall,
  Send,
  Video,
  VideoOff,
  Users,
  Wifi,
  WifiOff,
} from "lucide-react";

function RoomPage() {
  const socket = useSocket();
  const [remoteSocketId, setRemoteSocketId] = useState(null);
  const [myStream, setMyStream] = useState();
  const [remoteStream, setRemoteStream] = useState();

  const handleUserJoined = useCallback(({ email, id }) => {
    console.log(`Email ${email} joined room`);
    setRemoteSocketId(id);
  }, []);

  const handleCallUser = useCallback(async () => {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: true,
      video: true,
    });
    const offer = await peer.getOffer();
    socket.emit("user:call", { to: remoteSocketId, offer });
    setMyStream(stream);
  }, [remoteSocketId, socket]);

  const handleIncomingCall = useCallback(
    async ({ from, offer }) => {
      setRemoteSocketId(from);
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: true,
      });
      setMyStream(stream);
      console.log(`Incoming call from ${from}`);
      const ans = await peer.getAnswer(offer);
      socket.emit("call:accepted", { to: from, ans });
    },
    [socket]
  );

  const sendStreams = useCallback(() => {
    for (const track of myStream.getTracks()) {
      peer.peer.addTrack(track, myStream);
    }
  }, [myStream]);

  const handleCallAccepted = useCallback(
    ({ from, ans }) => {
      peer.setLocalDescription(ans);
      console.log("Call Accepted!");
      sendStreams();
    },
    [sendStreams]
  );

  const handleNegoNeeded = useCallback(async () => {
    const offer = await peer.getOffer();
    socket.emit("peer:nego:needed", { offer, to: remoteSocketId });
  }, [remoteSocketId, socket]);

  const handleNegoNeedIncomming = useCallback(
    async ({ from, offer }) => {
      const ans = await peer.getAnswer(offer);
      socket.emit("peer:nego:done", { to: from, ans });
    },
    [socket]
  );

  const handleNegoNeedFinal = useCallback(async ({ ans }) => {
    await peer.setLocalDescription(ans);
  }, []);

  useEffect(() => {
    peer.peer.addEventListener("negotiationneeded", handleNegoNeeded);
    return () => {
      peer.peer.removeEventListener("negotiationneeded", handleNegoNeeded);
    };
  }, [handleNegoNeeded]);

  useEffect(() => {
    peer.peer.addEventListener("track", async (ev) => {
      const remoteStream = ev.streams;
      console.log("Got tracks");
      setRemoteStream(remoteStream[0]);
    });
  }, []);

  useEffect(() => {
    socket.on("user:joined", handleUserJoined);
    socket.on("incoming:call", handleIncomingCall);
    socket.on("call:accepted", handleCallAccepted);
    socket.on("peer:nego:needed", handleNegoNeedIncomming);
    socket.on("peer:nego:final", handleNegoNeedFinal);
    return () => {
      socket.off("user:joined", handleUserJoined);
      socket.off("incoming:call", handleIncomingCall);
      socket.off("call:accepted", handleCallAccepted);
      socket.off("peer:nego:needed", handleNegoNeedIncomming);
      socket.off("peer:nego:final", handleNegoNeedFinal);
    };
  }, [
    handleCallAccepted,
    handleIncomingCall,
    handleNegoNeedFinal,
    handleNegoNeedIncomming,
    handleNegoNeeded,
    handleUserJoined,
    socket,
  ]);

  return (
    <div className='min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4'>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='text-center mb-8'>
          <h1 className='text-4xl font-bold text-gray-900 mb-2'>
            Video Chat Room
          </h1>
          <p className='text-gray-600'>Connect and communicate with others</p>
        </div>

        {/* Status Card */}
        <Card className='mb-8 shadow-lg border-0 bg-white/95 backdrop-blur-sm'>
          <CardHeader>
            <CardTitle className='flex items-center justify-between'>
              <span className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                Connection Status
              </span>
              <Badge
                variant={remoteSocketId ? "default" : "destructive"}
                className='flex items-center gap-1'
              >
                {remoteSocketId ? (
                  <>
                    <Wifi className='w-3 h-3' />
                    Connected
                  </>
                ) : (
                  <>
                    <WifiOff className='w-3 h-3' />
                    Waiting for participant...
                  </>
                )}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex flex-wrap gap-3'>
              {remoteSocketId && (
                <Button
                  onClick={handleCallUser}
                  className='bg-green-600 hover:bg-green-700 text-white'
                  size='lg'
                >
                  <PhoneCall className='w-4 h-4 mr-2' />
                  Start Call
                </Button>
              )}
              {myStream && (
                <Button
                  onClick={sendStreams}
                  variant='outline'
                  size='lg'
                  className='border-blue-200 hover:bg-blue-50'
                >
                  <Send className='w-4 h-4 mr-2' />
                  Send Stream
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Video Streams */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* My Stream */}
          {myStream && (
            <Card className='shadow-lg border-0 bg-white/95 backdrop-blur-sm'>
              <CardHeader>
                <CardTitle className='flex items-center gap-2 text-lg'>
                  <Video className='w-5 h-5 text-blue-600' />
                  Your Video
                </CardTitle>
              </CardHeader>
              <CardContent className='p-0'>
                <div className='relative rounded-lg overflow-hidden bg-gray-900'>
                  <ReactPlayer
                    playing
                    muted
                    width='100%'
                    height='300px'
                    url={myStream}
                    style={{ borderRadius: "0.5rem" }}
                  />
                  <div className='absolute top-3 left-3'>
                    <Badge
                      variant='secondary'
                      className='bg-black/50 text-white border-0'
                    >
                      You
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Remote Stream */}
          {remoteStream && (
            <Card className='shadow-lg border-0 bg-white/95 backdrop-blur-sm'>
              <CardHeader>
                <CardTitle className='flex items-center gap-2 text-lg'>
                  <Users className='w-5 h-5 text-green-600' />
                  Participant Video
                </CardTitle>
              </CardHeader>
              <CardContent className='p-0'>
                <div className='relative rounded-lg overflow-hidden bg-gray-900'>
                  <ReactPlayer
                    playing
                    width='100%'
                    height='300px'
                    url={remoteStream}
                    style={{ borderRadius: "0.5rem" }}
                  />
                  <div className='absolute top-3 left-3'>
                    <Badge
                      variant='secondary'
                      className='bg-black/50 text-white border-0'
                    >
                      Participant
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Placeholder when no streams */}
          {!myStream && !remoteStream && (
            <div className='lg:col-span-2'>
              <Card className='shadow-lg border-0 bg-white/95 backdrop-blur-sm'>
                <CardContent className='flex flex-col items-center justify-center py-16'>
                  <VideoOff className='w-16 h-16 text-gray-400 mb-4' />
                  <h3 className='text-xl font-semibold text-gray-700 mb-2'>
                    No Video Streams
                  </h3>
                  <p className='text-gray-500 text-center max-w-md'>
                    {remoteSocketId
                      ? "Click 'Start Call' to begin video communication"
                      : "Waiting for someone to join the room..."}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default RoomPage;
