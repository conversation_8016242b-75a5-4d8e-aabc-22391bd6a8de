# ShadCN Components Used in Video Call Application

This file tracks all ShadCN UI components used in the video call application.

## Installed Components

### Core Components

- **But<PERSON>** (`src/components/ui/button.jsx`) - Used for action buttons (Join, Call, Send Stream)
- **Input** (`src/components/ui/input.jsx`) - Used for form inputs (Email, Room Code)
- **Card** (`src/components/ui/card.jsx`) - Used for containers and layout sections
- **Label** (`src/components/ui/label.jsx`) - Used for form field labels
- **Badge** (`src/components/ui/badge.jsx`) - Used for status indicators (Connected/Waiting)

## Usage Locations

### Lobby Page (App.jsx)

- **Card**: Main container for the lobby form with CardHeader, CardTitle, CardDescription, and CardContent
- **Input**: Email and Room Code input fields with proper styling
- **Label**: Form field labels with htmlFor attributes
- **Button**: Join button with gradient styling and disabled state
- **Icons**: Video icon in header, Users icon in button

### Video Chat Room Page (RoomPage.jsx)

- **Card**: Multiple cards for status container and video stream containers
- **Badge**: Connection status indicator with variants (default for connected, destructive for waiting)
- **Button**: Call and Send Stream buttons with different variants and sizes
- **Icons**: PhoneCall, Send, Video, VideoOff, Users, Wifi, WifiOff for various UI elements

## Component Variants Used

- **Button**:
  - Default variant with custom gradient styling for primary actions
  - Outline variant for secondary actions
  - Large size for prominent buttons
- **Badge**:
  - Default variant for connected status
  - Destructive variant for waiting status
  - Secondary variant for video stream labels
- **Card**: Default variant with shadow-lg and backdrop-blur-sm styling

## Styling Enhancements

- Gradient backgrounds for modern look
- Backdrop blur effects for glass morphism
- Responsive grid layouts for video streams
- Proper spacing and typography hierarchy
- Icon integration with Lucide React

## Installation Date

- Initial setup: 2025-06-01
