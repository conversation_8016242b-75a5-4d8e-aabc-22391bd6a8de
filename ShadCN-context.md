# ShadCN Components Used in Video Call Application

This file tracks all ShadCN UI components used in the video call application.

## Installed Components

### Core Components

- **Button** (`src/components/ui/button.jsx`) - Used for action buttons (Join, Call, Send Stream)
- **Input** (`src/components/ui/input.jsx`) - Used for form inputs (Email, Room Code)
- **Card** (`src/components/ui/card.jsx`) - Used for containers and layout sections
- **Label** (`src/components/ui/label.jsx`) - Used for form field labels
- **Badge** (`src/components/ui/badge.jsx`) - Used for status indicators (Connected/Waiting)

### Enhanced Components (Phase 1)

- **Switch** (`src/components/ui/switch.jsx`) - Used for camera/microphone toggles
- **Slider** (`src/components/ui/slider.jsx`) - Used for volume controls
- **Tooltip** (`src/components/ui/tooltip.jsx`) - Used for button explanations

## Usage Locations

### Lobby Page (App.jsx)

- **Card**: Main container for the lobby form with CardHeader, CardTitle, CardDescription, and CardContent
- **Input**: Email and Room Code input fields with proper styling
- **Label**: Form field labels with htmlFor attributes
- **Button**: Join button with gradient styling and disabled state
- **Icons**: Video icon in header, Users icon in button

### Video Chat Room Page (RoomPage.jsx)

- **Card**: Multiple cards for status container and video stream containers
- **Badge**: Connection status indicator with variants (default for connected, destructive for waiting)
- **Button**: Call and Send Stream buttons with different variants and sizes
- **Icons**: PhoneCall, Send, Video, VideoOff, Users, Wifi, WifiOff, Signal, SignalHigh, SignalLow for various UI elements

### Video Controls Component (VideoControls.jsx)

- **Button**: Video control buttons (camera, microphone, end call, fullscreen, settings)
- **Switch**: Toggle switches for camera and microphone controls
- **Slider**: Volume control slider with range 0-100
- **Tooltip**: Helpful tooltips for all control buttons with TooltipProvider, TooltipTrigger, TooltipContent
- **Card**: Container for the control panel with dark theme styling
- **Icons**: Video, VideoOff, Mic, MicOff, PhoneOff, Volume2, VolumeX, Maximize, Minimize, Settings

## Component Variants Used

- **Button**:
  - Default variant with custom gradient styling for primary actions
  - Outline variant for secondary actions
  - Large size for prominent buttons
- **Badge**:
  - Default variant for connected status
  - Destructive variant for waiting status
  - Secondary variant for video stream labels
- **Card**: Default variant with shadow-lg and backdrop-blur-sm styling

## Styling Enhancements

- Gradient backgrounds for modern look
- Backdrop blur effects for glass morphism
- Responsive grid layouts for video streams
- Proper spacing and typography hierarchy
- Icon integration with Lucide React

## Installation Date

- Initial setup: 2025-06-01
